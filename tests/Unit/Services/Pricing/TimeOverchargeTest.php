<?php

namespace Tests\Unit\Services\Pricing;

use App\Models\PricingRules;
use App\Services\PricingService;
use Carbon\Carbon;
use Tests\TestCase;

class TimeOverchargeTest extends TestCase
{
    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();

        // Create or update global pricing rules with a time threshold
        $this->globalRules = PricingRules::first();
        if (! $this->globalRules) {
            $this->globalRules = PricingRules::create([
                'global_base_price' => 3.00,
                'global_price_per_km' => 2.00,
                'time_threshold_percentage' => 20.00, // 20% threshold
            ]);
        } else {
            $this->globalRules->update([
                'time_threshold_percentage' => 20.00,
            ]);
            $this->globalRules->refresh();
        }
    }

    /**
     * Test that no overcharge is applied when there is no time difference.
     */
    public function test_no_overcharge_when_no_time_difference(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(30); // Same as estimated

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that no time overcharge was applied
        $this->assertIsArray($pricingData);
        $this->assertArrayHasKey('adjustments', $pricingData);

        // Check if time overcharge exists in adjustments
        $timeOverchargeAmount = 0;
        foreach ($pricingData['adjustments'] as $adjustment) {
            if ($adjustment['type'] === 'time_overcharge') {
                $timeOverchargeAmount = $adjustment['amount'] ?? 0;
                break;
            }
        }

        // No overcharge should be applied when times are equal
        $this->assertEquals(0, $timeOverchargeAmount);
    }

    /**
     * Test that no overcharge is applied when time difference is within threshold.
     */
    public function test_no_overcharge_when_time_difference_within_threshold(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(35); // 16.67% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that no time overcharge was applied (within threshold)
        $this->assertIsArray($pricingData);
        $this->assertArrayHasKey('adjustments', $pricingData);

        // Check if time overcharge exists in adjustments
        $timeOverchargeAmount = 0;
        foreach ($pricingData['adjustments'] as $adjustment) {
            if ($adjustment['type'] === 'time_overcharge') {
                $timeOverchargeAmount = $adjustment['amount'] ?? 0;
                break;
            }
        }

        // No overcharge should be applied when within threshold
        $this->assertEquals(0, $timeOverchargeAmount);
    }

    /**
     * Test that overcharge is applied when time difference exceeds threshold.
     */
    public function test_overcharge_when_time_difference_exceeds_threshold(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(45); // 50% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that time overcharge was applied
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(30, $pricingData['time_overcharge']['overcharge_percentage']);

        // Calculate expected subtotal with overcharge
        $subtotal = $pricingData['subtotal'];
        $expectedSubtotalWithOvercharge = $subtotal * 1.3; // 30% overcharge

        $this->assertEquals(round($expectedSubtotalWithOvercharge, 2), $pricingData['subtotal_with_overcharge']);
    }

    /**
     * Test that no overcharge is applied when actual time is less than estimated.
     */
    public function test_no_overcharge_when_actual_time_less_than_estimated(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(25); // 16.67% shorter

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that no time overcharge was applied
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(0, $pricingData['time_overcharge']['overcharge_percentage']);
        $this->assertEquals($pricingData['subtotal'], $pricingData['subtotal_with_overcharge']);
    }

    /**
     * Test that the overcharge is correctly applied to the final price.
     */
    public function test_overcharge_is_correctly_applied_to_final_price(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(45); // 50% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that time overcharge was applied to the final price
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(30, $pricingData['time_overcharge']['overcharge_percentage']);

        // Calculate expected prices
        $subtotal = $pricingData['subtotal'];
        $expectedSubtotalWithOvercharge = $subtotal * 1.3; // 30% overcharge
        $expectedAdjustedPrice = $expectedSubtotalWithOvercharge + ($pricingData['equipment_total'] ?? 0);

        $this->assertEquals(round($expectedAdjustedPrice, 2), $pricingData['adjusted_price']);
        $this->assertEquals(round($expectedAdjustedPrice, 2), $pricingData['total']);
    }

    /**
     * Test that the global rules threshold is used correctly.
     */
    public function test_global_rules_threshold_is_used_correctly(): void
    {
        // Update global rules with a different threshold
        $this->globalRules->update([
            'time_threshold_percentage' => 10.00, // 10% threshold
        ]);
        $this->globalRules->refresh();

        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(36); // 20% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that time overcharge was applied with the new threshold
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(10, $pricingData['time_overcharge']['threshold_percentage']);
        $this->assertEquals(10, $pricingData['time_overcharge']['overcharge_percentage']);

        // Reset the threshold for other tests
        $this->globalRules->update([
            'time_threshold_percentage' => 20.00, // 20% threshold
        ]);
        $this->globalRules->refresh();
    }
}
