<?php

namespace Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication, RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed essential data for tests
        $this->seedEssentialData();
    }

    /**
     * Seed essential data needed for most tests
     */
    protected function seedEssentialData(): void
    {
        try {
            // Create basic pricing rules if they don't exist
            if (! \App\Models\PricingRules::exists()) {
                \App\Models\PricingRules::create([
                    'global_base_price' => 10.00,
                    'global_price_per_km' => 30.00,
                    'time_threshold_percentage' => 50.00,
                ]);
            }

            // Create basic vehicle types if they don't exist
            if (! \App\Models\VehicleType::where('id', 1)->exists()) {
                \App\Models\VehicleType::create([
                    'id' => 1,
                    'name_en' => 'Economy',
                    'name_ar' => 'اقتصادي',
                    'category' => 'passenger',
                    'status' => true,
                    'additional_base_fare' => 0.00,
                    'additional_price_per_km' => 0.00,
                    'base_fare_adjustment_type' => 'percentage',
                    'distance_fare_adjustment_type' => 'percentage',
                    'base_fare_adjustment' => 0.00,
                    'distance_fare_adjustment' => 0.00,
                ]);
            }

            // Create basic area for testing if it doesn't exist
            if (! \App\Models\Area::where('id', 1)->exists()) {
                \App\Models\Area::create([
                    'id' => 1,
                    'name_en' => 'Test Area',
                    'name_ar' => 'منطقة تجريبية',
                    'is_active' => true,
                    'base_fare' => 0.00,
                    'distance_fare' => 0.00,
                    'base_fare_adjustment_type' => 'percentage',
                    'distance_fare_adjustment_type' => 'percentage',
                    'base_fare_adjustment' => 0.00,
                    'distance_fare_adjustment' => 0.00,
                ]);
            }
        } catch (\Exception $e) {
            // If seeding fails, continue without essential data
            // Individual tests can create their own data as needed
        }
    }
}
