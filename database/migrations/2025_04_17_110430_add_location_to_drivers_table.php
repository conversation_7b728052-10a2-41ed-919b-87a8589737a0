<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using SQLite and handle differently
        if (config('database.default') === 'sqlite' ||
            (config('database.connections.'.config('database.default').'.driver') === 'sqlite')) {

            // For SQLite, use TEXT to store location as WKT or JSON
            Schema::table('drivers', function (Blueprint $table) {
                $table->text('location')->nullable();
            });
        } else {
            // For PostgreSQL/MySQL with PostGIS
            Schema::table('drivers', function (Blueprint $table) {
                DB::statement('ALTER TABLE drivers ADD COLUMN location GEOGRAPHY(POINT, 4326)');
            });
        }

        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('last_lat');
            $table->dropColumn('last_lng');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn('location');
        });

        Schema::table('vehicles', function (Blueprint $table) {
            $table->decimal('last_lat', 10)->nullable();
            $table->decimal('last_lng', 10)->nullable();
        });
    }
};
