<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using SQLite and handle differently
        if (config('database.default') === 'sqlite' ||
            (config('database.connections.'.config('database.default').'.driver') === 'sqlite')) {

            // For SQLite, we need to recreate the table to change column type
            Schema::table('trips', function (Blueprint $table) {
                // First, drop the index if it exists
                try {
                    $table->dropIndex('trips_status_index');
                } catch (\Exception $e) {
                    // Index might not exist, continue
                }
            });

            // Now drop and recreate the column
            Schema::table('trips', function (Blueprint $table) {
                $table->dropColumn('status');
            });

            Schema::table('trips', function (Blueprint $table) {
                $table->enum('status', [
                    'pending', 'timeout', 'dispatched', 'canceled', 'rejected',
                    'assigned', 'driver_arriving', 'driver_arrived', 'no_show',
                    'on_trip', 'waiting_for_driver_confirmation', 'completed',
                ])->default('pending');
            });
        } else {
            // For other databases (MySQL, PostgreSQL)
            Schema::table('trips', function (Blueprint $table) {
                $table->dropColumn('status');

                $table->enum('status', [
                    'pending', 'timeout', 'dispatched', 'canceled', 'rejected',
                    'assigned', 'driver_arriving', 'driver_arrived', 'no_show',
                    'on_trip', 'waiting_for_driver_confirmation', 'completed',
                ])->default('pending');
            });
        }
    }

    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropColumn('status');

            // Reverting back to the original column type (assuming it was string)
            $table->string('status')->default('pending');
        });
    }
};
